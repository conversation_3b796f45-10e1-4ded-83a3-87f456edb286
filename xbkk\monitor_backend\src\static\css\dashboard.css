/* 星巴克风控绕过系统 - 监控后台样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.dashboard-body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1030;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.1rem;
}

/* 侧边栏 */
.sidebar {
    position: fixed;
    top: 56px;
    left: 0;
    width: 250px;
    height: calc(100vh - 56px);
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    overflow-y: auto;
    z-index: 1020;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.sidebar-header h5 {
    margin: 0;
    font-weight: 600;
    color: #ecf0f1;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.menu-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-item a:hover {
    background: rgba(255,255,255,0.1);
    color: #ecf0f1;
    padding-left: 25px;
}

.menu-item.active a {
    background: linear-gradient(90deg, #3498db, #2980b9);
    color: white;
    box-shadow: inset 3px 0 0 #fff;
}

.menu-item a i {
    margin-right: 12px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.menu-item a span {
    flex: 1;
}

.menu-item .badge {
    margin-left: auto;
    font-size: 0.75rem;
}

/* 主内容区域 */
.main-content {
    margin-left: 250px;
    margin-top: 56px;
    padding: 30px;
    min-height: calc(100vh - 56px);
}

/* 页面内容 */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.page-header h2 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
}

.page-header p {
    margin: 0;
    font-size: 0.95rem;
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07);
    border: none;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
}

.stats-card.bg-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.stats-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2.5rem;
    opacity: 0.3;
}

.stats-content h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-content p {
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stats-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stats-change.positive {
    color: rgba(255,255,255,0.9);
}

.stats-change.negative {
    color: rgba(255,255,255,0.7);
}

.stats-change i {
    margin-right: 4px;
}

/* 图表卡片 */
.chart-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07);
    overflow: hidden;
}

.chart-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.chart-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-controls button {
    padding: 5px 12px;
    font-size: 0.8rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.chart-controls button.active {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.chart-body {
    padding: 25px;
    height: 300px;
    position: relative;
}

/* 状态卡片 */
.status-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07);
    overflow: hidden;
}

.status-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.status-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-indicator.online {
    background: #d4edda;
    color: #155724;
}

.status-indicator.offline {
    background: #f8d7da;
    color: #721c24;
}

.status-body {
    padding: 25px;
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    width: 100px;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.status-item .progress {
    flex: 1;
    height: 8px;
    margin: 0 15px;
    border-radius: 10px;
    background: #e9ecef;
}

.status-item .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

.status-value {
    width: 50px;
    text-align: right;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

/* 活动卡片 */
.activity-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07);
    overflow: hidden;
}

.activity-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.activity-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.activity-body {
    padding: 15px 0;
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 12px 25px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.activity-icon.text-success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.activity-icon.text-warning {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.activity-icon.text-danger {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.activity-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.8rem;
    color: #6c757d;
}

.activity-customer {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
}

.activity-status {
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 20px 15px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .status-item .progress {
        width: 100%;
        margin: 0;
    }
    
    .activity-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar,
.activity-body::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.activity-body::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar::-webkit-scrollbar-thumb,
.activity-body::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.activity-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 模态框样式增强 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 表格样式 */
.table-borderless td {
    border: none;
    padding: 8px 0;
}

.table-borderless td:first-child {
    color: #6c757d;
    font-weight: 500;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* 按钮样式增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.8rem;
}

/* 实时日志样式 */
.log-entry {
    padding: 8px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    background: #f8f9fa;
    border-left: 3px solid #28a745;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.log-entry.suspicious {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.log-entry:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.log-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.log-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.customer-id {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
}

.api-endpoint {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.status-success {
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-error {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
}

.response-time {
    background: #17a2b8;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
}

.suspicious-flag {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* WebSocket状态指示器 */
#websocketStatus {
    font-size: 0.75rem;
    transition: all 0.3s ease;
}

#websocketStatus.connected {
    background-color: #28a745 !important;
}

#websocketStatus.disconnected {
    background-color: #dc3545 !important;
}

#websocketStatus.connecting {
    background-color: #ffc107 !important;
    color: #212529 !important;
}
