#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
星巴克风控绕过系统 - 独立监控后台
专用于监控所有客户接口使用情况
"""

import os
import json
import sqlite3
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, Form, WebSocket, WebSocketDisconnect
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import uvicorn
import aiofiles
import aiohttp
from cryptography.fernet import Fernet
import logging
import jwt
import secrets

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/monitor_backend.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# WebSocket连接管理器
class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {str(e)}")
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        if not self.active_connections:
            return

        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播WebSocket消息失败: {str(e)}")
                disconnected.add(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.active_connections.discard(connection)

    async def broadcast_json(self, data: dict):
        """广播JSON数据给所有连接"""
        message = json.dumps(data, ensure_ascii=False)
        await self.broadcast(message)

# 创建WebSocket管理器实例
websocket_manager = WebSocketManager()

# 创建FastAPI应用
app = FastAPI(
    title="星巴克风控绕过系统 - 监控后台",
    version="1.0.0"
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="./src/static"), name="static")

# 模板配置
templates = Jinja2Templates(directory="./src/templates")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全配置
security = HTTPBearer()

# 加密密钥
ENCRYPTION_KEY = os.getenv('MONITOR_ENCRYPTION_KEY', Fernet.generate_key())
if isinstance(ENCRYPTION_KEY, str):
    ENCRYPTION_KEY = ENCRYPTION_KEY.encode()
cipher_suite = Fernet(ENCRYPTION_KEY)

# JWT配置
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', secrets.token_urlsafe(32))
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = int(os.getenv('JWT_EXPIRE_HOURS', '24'))

# 固定管理员账户
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME', 'admin')
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123456')

# API认证令牌
MONITOR_BACKEND_TOKEN = os.getenv('MONITOR_BACKEND_TOKEN', 'monitor_backend_secret_token_2025')

# 数据模型
class LogEntry(BaseModel):
    timestamp: str
    client_ip: str
    customer_id: str
    user_type: Optional[str] = "customer"  # customer, admin, anonymous, internal
    api_endpoint: str
    request_method: str
    request_headers: Dict[str, Any]
    request_body: Optional[str] = None
    response_status: int
    response_body: Optional[str] = None
    response_time: float
    user_agent: str
    is_suspicious: bool = False
    risk_score: float = 0.0

class CleanupRequest(BaseModel):
    customer_id: Optional[str] = None
    confirm_deletion: bool = False

class AlertConfig(BaseModel):
    email_enabled: bool = True
    webhook_enabled: bool = True
    threshold_requests_per_minute: int = 100
    threshold_error_rate: float = 0.1

class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class LoginResponse(BaseModel):
    success: bool
    message: str
    access_token: Optional[str] = None
    token_type: str = "bearer"

# 数据库管理器
class MonitorDatabase:
    def __init__(self, db_path: str = "./monitor_logs.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                client_ip TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                user_type TEXT DEFAULT 'customer',
                api_endpoint TEXT NOT NULL,
                request_method TEXT NOT NULL,
                request_headers TEXT NOT NULL,
                request_body TEXT,
                response_status INTEGER NOT NULL,
                response_body TEXT,
                response_time REAL NOT NULL,
                user_agent TEXT NOT NULL,
                is_suspicious BOOLEAN DEFAULT FALSE,
                risk_score REAL DEFAULT 0.0,
                encrypted_data TEXT NOT NULL
            )
        ''')

        # 添加user_type字段（如果不存在）
        try:
            cursor.execute('ALTER TABLE api_logs ADD COLUMN user_type TEXT DEFAULT "customer"')
        except sqlite3.OperationalError:
            # 字段已存在，忽略错误
            pass
        
        # 创建客户统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer_stats (
                customer_id TEXT PRIMARY KEY,
                total_requests INTEGER DEFAULT 0,
                total_errors INTEGER DEFAULT 0,
                last_access TEXT,
                risk_level TEXT DEFAULT 'LOW',
                is_blocked BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # 创建告警表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                customer_id TEXT,
                message TEXT NOT NULL,
                severity TEXT NOT NULL,
                is_resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()

        logger.info("监控后台数据库初始化完成")
    
    def encrypt_data(self, data: str) -> str:
        """加密敏感数据"""
        return cipher_suite.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        return cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def log_api_request(self, log_entry: LogEntry):
        """记录API请求日志"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 加密敏感数据
            sensitive_data = {
                'request_headers': log_entry.request_headers,
                'request_body': log_entry.request_body,
                'response_body': log_entry.response_body
            }
            encrypted_data = self.encrypt_data(json.dumps(sensitive_data))

            cursor.execute('''
                INSERT INTO api_logs (
                    timestamp, client_ip, customer_id, user_type, api_endpoint, request_method,
                    request_headers, request_body, response_status, response_body,
                    response_time, user_agent, is_suspicious, risk_score, encrypted_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                log_entry.timestamp, log_entry.client_ip, log_entry.customer_id,
                getattr(log_entry, 'user_type', 'customer'),
                log_entry.api_endpoint, log_entry.request_method,
                json.dumps(log_entry.request_headers), log_entry.request_body,
                log_entry.response_status, log_entry.response_body,
                log_entry.response_time, log_entry.user_agent,
                log_entry.is_suspicious, log_entry.risk_score, encrypted_data
            ))

            conn.commit()
            conn.close()

            # 更新客户统计
            self.update_customer_stats(log_entry.customer_id, log_entry.response_status >= 400)

            logger.debug(f"API日志记录成功 - 客户: {log_entry.customer_id}, 端点: {log_entry.api_endpoint}, 状态: {log_entry.response_status}")

        except Exception as e:
            logger.error(f"记录API日志失败: {str(e)}")
            if 'conn' in locals():
                conn.close()
    
    def update_customer_stats(self, customer_id: str, is_error: bool):
        """更新客户统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查客户是否已存在
            cursor.execute('SELECT total_requests, total_errors FROM customer_stats WHERE customer_id = ?', (customer_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有记录
                new_total_requests = existing[0] + 1
                new_total_errors = existing[1] + (1 if is_error else 0)

                cursor.execute('''
                    UPDATE customer_stats
                    SET total_requests = ?, total_errors = ?, last_access = ?
                    WHERE customer_id = ?
                ''', (new_total_requests, new_total_errors, datetime.now().isoformat(), customer_id))
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO customer_stats (customer_id, total_requests, total_errors, last_access, risk_level)
                    VALUES (?, ?, ?, ?, ?)
                ''', (customer_id, 1, 1 if is_error else 0, datetime.now().isoformat(), 'LOW'))

            conn.commit()
            conn.close()

            logger.debug(f"客户统计更新成功 - 客户: {customer_id}, 错误: {is_error}")

        except Exception as e:
            logger.error(f"更新客户统计失败: {str(e)}")
            if 'conn' in locals():
                conn.close()
    
    def get_logs(self, limit: int = 100, customer_id: Optional[str] = None) -> List[Dict]:
        """获取日志记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
            SELECT * FROM api_logs 
            WHERE (? IS NULL OR customer_id = ?)
            ORDER BY timestamp DESC 
            LIMIT ?
        '''
        
        cursor.execute(query, (customer_id, customer_id, limit))
        rows = cursor.fetchall()
        
        columns = [description[0] for description in cursor.description]
        logs = []
        
        for row in rows:
            log_dict = dict(zip(columns, row))
            # 解密敏感数据
            try:
                encrypted_data = log_dict.get('encrypted_data', '{}')
                decrypted_data = json.loads(self.decrypt_data(encrypted_data))
                log_dict.update(decrypted_data)
            except:
                pass
            logs.append(log_dict)
        
        conn.close()
        return logs
    
    def get_customer_stats(self) -> List[Dict]:
        """获取客户统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM customer_stats ORDER BY total_requests DESC')
            rows = cursor.fetchall()

            columns = [description[0] for description in cursor.description]
            stats = [dict(zip(columns, row)) for row in rows]

            conn.close()
            return stats
        except sqlite3.OperationalError as e:
            # 表不存在，重新初始化数据库
            conn.close()
            self.init_database()
            return []
    
    def cleanup_data(self, customer_id: Optional[str] = None) -> Dict[str, int]:
        """清理数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if customer_id:
            # 清理特定客户数据
            cursor.execute('DELETE FROM api_logs WHERE customer_id = ?', (customer_id,))
            logs_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM customer_stats WHERE customer_id = ?', (customer_id,))
            stats_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM alerts WHERE customer_id = ?', (customer_id,))
            alerts_deleted = cursor.rowcount
        else:
            # 清理所有数据
            cursor.execute('DELETE FROM api_logs')
            logs_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM customer_stats')
            stats_deleted = cursor.rowcount
            
            cursor.execute('DELETE FROM alerts')
            alerts_deleted = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            'logs_deleted': logs_deleted,
            'stats_deleted': stats_deleted,
            'alerts_deleted': alerts_deleted
        }

    def create_alert(self, alert_type: str, customer_id: str, message: str, severity: str):
        """创建告警"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO alerts (timestamp, alert_type, customer_id, message, severity)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            alert_type,
            customer_id,
            message,
            severity
        ))

        conn.commit()
        conn.close()

    def update_customer_stats(self, customer_id: str, is_error: bool):
        """更新客户统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查客户是否已存在
            cursor.execute('SELECT total_requests, total_errors FROM customer_stats WHERE customer_id = ?', (customer_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有记录
                new_total_requests = existing[0] + 1
                new_total_errors = existing[1] + (1 if is_error else 0)

                cursor.execute('''
                    UPDATE customer_stats
                    SET total_requests = ?, total_errors = ?, last_access = ?
                    WHERE customer_id = ?
                ''', (new_total_requests, new_total_errors, datetime.now().isoformat(), customer_id))
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO customer_stats (customer_id, total_requests, total_errors, last_access, risk_level)
                    VALUES (?, ?, ?, ?, ?)
                ''', (customer_id, 1, 1 if is_error else 0, datetime.now().isoformat(), 'LOW'))

            conn.commit()
            conn.close()

            logger.debug(f"客户统计更新成功 - 客户: {customer_id}, 错误: {is_error}")

        except Exception as e:
            logger.error(f"更新客户统计失败: {str(e)}")
            if 'conn' in locals():
                conn.close()

# 全局数据库实例
db = MonitorDatabase()

# 安全检查器
class SecurityChecker:
    def __init__(self):
        self.suspicious_patterns = [
            'eval(',
            'exec(',
            'import os',
            'subprocess',
            '__import__',
            'open(',
            'file(',
            'input(',
            'raw_input(',
            'compile(',
            'reload(',
            'getattr(',
            'setattr(',
            'delattr(',
            'hasattr(',
            'globals(',
            'locals(',
            'vars(',
            'dir(',
            'help(',
            'copyright',
            'credits',
            'license',
            'quit',
            'exit',
            'sys.exit',
            'os.system',
            'os.popen',
            'os.spawn',
            'os.fork',
            'os.kill',
            'socket.',
            'urllib',
            'requests.',
            'http.',
            'ftp.',
            'telnet.',
            'ssh.',
            'paramiko.',
            'fabric.',
            'ansible.',
            'docker.',
            'kubernetes.',
            'kubectl.',
            'bash',
            'sh',
            'cmd',
            'powershell',
            'wget',
            'curl',
            'nc',
            'netcat',
            'nmap',
            'sqlmap',
            'metasploit',
            'msfconsole',
            'msfvenom',
            'hydra',
            'john',
            'hashcat',
            'aircrack',
            'wireshark',
            'tcpdump',
            'nessus',
            'openvas',
            'nikto',
            'dirb',
            'gobuster',
            'wfuzz',
            'burp',
            'zap',
            'sqlinjection',
            'xss',
            'csrf',
            'lfi',
            'rfi',
            'xxe',
            'ssrf',
            'rce',
            'backdoor',
            'webshell',
            'reverse_shell',
            'bind_shell',
            'payload',
            'exploit',
            'vulnerability',
            'pentest',
            'hack',
            'crack',
            'bypass',
            'injection',
            'overflow',
            'privilege',
            'escalation',
            'persistence',
            'lateral',
            'movement',
            'exfiltration',
            'c2',
            'command_control',
            'botnet',
            'malware',
            'trojan',
            'virus',
            'worm',
            'rootkit',
            'keylogger',
            'ransomware',
            'cryptominer',
            'steganography',
            'covert',
            'channel',
            'tunnel',
            'proxy',
            'socks',
            'tor',
            'onion',
            'darkweb',
            'anonymous',
            'vpn',
            'encryption',
            'decryption',
            'cipher',
            'hash',
            'salt',
            'rainbow',
            'dictionary',
            'bruteforce',
            'wordlist',
            'rockyou',
            'kali',
            'parrot',
            'blackarch',
            'pentoo',
            'backtrack'
        ]
    
    def check_request(self, request_data: str) -> tuple[bool, float]:
        """检查请求是否可疑"""
        if not request_data:
            return False, 0.0
        
        request_lower = request_data.lower()
        suspicious_count = 0
        
        for pattern in self.suspicious_patterns:
            if pattern in request_lower:
                suspicious_count += 1
        
        risk_score = min(suspicious_count / 10.0, 1.0)
        is_suspicious = risk_score > 0.3
        
        return is_suspicious, risk_score

# 全局安全检查器
security_checker = SecurityChecker()

# JWT工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRE_HOURS)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_jwt_token(token: str):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return username
    except jwt.PyJWTError:
        return None

# 认证依赖
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    token = credentials.credentials

    # 检查是否是监控后台令牌
    if token == MONITOR_BACKEND_TOKEN:
        return token

    # 检查是否是JWT令牌
    username = verify_jwt_token(token)
    if username is not None:
        return token

    raise HTTPException(status_code=401, detail="无效的访问令牌")

    return token

async def verify_web_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证Web界面JWT令牌"""
    token = credentials.credentials
    username = verify_jwt_token(token)

    if username is None:
        raise HTTPException(status_code=401, detail="无效的访问令牌")

    return username

def authenticate_user(username: str, password: str) -> bool:
    """验证用户凭据"""
    return username == ADMIN_USERNAME and password == ADMIN_PASSWORD

async def get_realtime_stats() -> dict:
    """获取实时统计数据"""
    try:
        # 获取最近的日志数据
        recent_logs = db.get_logs(limit=1000)

        # 计算基础统计
        total_requests = len(recent_logs)
        suspicious_requests = sum(1 for log in recent_logs if log.get('is_suspicious', False))
        error_requests = sum(1 for log in recent_logs if log.get('response_status', 200) >= 400)

        # 计算平均响应时间
        response_times = [log.get('response_time', 0) for log in recent_logs if log.get('response_time')]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        # 获取活跃客户数
        active_customers = len(set(log.get('customer_id', '') for log in recent_logs if log.get('customer_id')))

        # 计算成功率
        success_rate = ((total_requests - error_requests) / total_requests * 100) if total_requests > 0 else 100

        return {
            "total_requests": total_requests,
            "active_customers": active_customers,
            "suspicious_requests": suspicious_requests,
            "error_requests": error_requests,
            "avg_response_time": round(avg_response_time, 2),
            "success_rate": round(success_rate, 1),
            "websocket_connections": len(websocket_manager.active_connections)
        }

    except Exception as e:
        logger.error(f"获取实时统计失败: {str(e)}")
        return {
            "total_requests": 0,
            "active_customers": 0,
            "suspicious_requests": 0,
            "error_requests": 0,
            "avg_response_time": 0,
            "success_rate": 100,
            "websocket_connections": 0
        }

# Web界面路由
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路径重定向到登录页面"""
    return RedirectResponse(url="/login")

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard_page(request: Request):
    """管理后台页面"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.post("/api/auth/login")
async def login(login_data: LoginRequest):
    """用户登录"""
    if not authenticate_user(login_data.username, login_data.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 创建访问令牌
    access_token_expires = timedelta(hours=JWT_EXPIRE_HOURS)
    access_token = create_access_token(
        data={"sub": login_data.username},
        expires_delta=access_token_expires
    )

    return LoginResponse(
        success=True,
        message="登录成功",
        access_token=access_token,
        token_type="bearer"
    )

@app.get("/api/auth/verify")
async def verify_auth(username: str = Depends(verify_web_token)):
    """验证用户认证状态"""
    return {
        "success": True,
        "username": username,
        "message": "认证有效"
    }

@app.websocket("/ws/monitor")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket监控数据推送端点"""
    await websocket_manager.connect(websocket)
    try:
        # 发送初始连接确认
        await websocket_manager.send_personal_message(
            json.dumps({
                "type": "connection",
                "message": "WebSocket连接已建立",
                "timestamp": datetime.now().isoformat()
            }, ensure_ascii=False),
            websocket
        )

        # 保持连接活跃
        while True:
            try:
                # 等待客户端消息（心跳包）
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get("type") == "ping":
                    # 响应心跳包
                    await websocket_manager.send_personal_message(
                        json.dumps({
                            "type": "pong",
                            "timestamp": datetime.now().isoformat()
                        }, ensure_ascii=False),
                        websocket
                    )
                elif message.get("type") == "request_stats":
                    # 客户端请求统计数据
                    stats_data = await get_realtime_stats()
                    await websocket_manager.send_personal_message(
                        json.dumps({
                            "type": "stats_update",
                            "data": stats_data,
                            "timestamp": datetime.now().isoformat()
                        }, ensure_ascii=False),
                        websocket
                    )

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {str(e)}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket连接错误: {str(e)}")
    finally:
        websocket_manager.disconnect(websocket)

@app.post("/api/auth/logout")
async def logout():
    """用户登出"""
    return {
        "success": True,
        "message": "登出成功"
    }

# API路由
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "monitor_backend",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/api/log")
async def log_api_request(log_entry: LogEntry, token: str = Depends(verify_token)):
    """记录API请求日志"""
    try:
        # 安全检查
        request_data = f"{log_entry.request_body} {json.dumps(log_entry.request_headers)}"
        is_suspicious, risk_score = security_checker.check_request(request_data)

        log_entry.is_suspicious = is_suspicious
        log_entry.risk_score = risk_score

        # 记录日志
        db.log_api_request(log_entry)

        # 更新客户统计
        db.update_customer_stats(log_entry.customer_id, log_entry.response_status >= 400)

        # 如果检测到可疑行为，发送告警
        if is_suspicious:
            logger.warning(f"检测到可疑请求 - 客户: {log_entry.customer_id}, 风险评分: {risk_score}")

            # 记录告警
            alert_message = f"客户 {log_entry.customer_id} 存在可疑行为，风险评分: {risk_score}"
            db.create_alert("SUSPICIOUS_BEHAVIOR", log_entry.customer_id, alert_message, "WARNING")

        # 通过WebSocket推送实时数据更新
        try:
            stats_data = await get_realtime_stats()
            await websocket_manager.broadcast_json({
                "type": "stats_update",
                "data": stats_data,
                "timestamp": datetime.now().isoformat()
            })

            # 推送新日志记录
            await websocket_manager.broadcast_json({
                "type": "new_log",
                "data": {
                    "timestamp": log_entry.timestamp,
                    "customer_id": log_entry.customer_id,
                    "api_endpoint": log_entry.api_endpoint,
                    "request_method": log_entry.request_method,
                    "response_status": log_entry.response_status,
                    "response_time": log_entry.response_time,
                    "is_suspicious": is_suspicious,
                    "risk_score": risk_score
                },
                "timestamp": datetime.now().isoformat()
            })
        except Exception as ws_error:
            logger.error(f"WebSocket推送失败: {str(ws_error)}")

        return {
            "success": True,
            "message": "日志记录成功",
            "is_suspicious": is_suspicious,
            "risk_score": risk_score
        }

    except Exception as e:
        logger.error(f"记录日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"记录日志失败: {str(e)}")

@app.post("/api/logs/record")
async def record_customer_log(log_entry: LogEntry, token: str = Depends(verify_token)):
    """专用于主系统记录客户日志的端点"""
    try:
        # 安全检查
        request_data = f"{log_entry.request_body} {json.dumps(log_entry.request_headers)}"
        is_suspicious, risk_score = security_checker.check_request(request_data)

        log_entry.is_suspicious = is_suspicious
        log_entry.risk_score = risk_score

        # 记录日志
        db.log_api_request(log_entry)

        # 更新客户统计
        db.update_customer_stats(log_entry.customer_id, log_entry.response_status >= 400)

        # 如果检测到可疑行为，发送告警
        if is_suspicious:
            logger.warning(f"客户可疑行为检测 - 客户: {log_entry.customer_id}, "
                         f"IP: {log_entry.client_ip}, 风险评分: {risk_score}")

            # 记录告警
            alert_message = f"客户 {log_entry.customer_id} 存在可疑行为，风险评分: {risk_score}"
            db.create_alert("SUSPICIOUS_BEHAVIOR", log_entry.customer_id, alert_message, "WARNING")

        # 通过WebSocket推送实时数据更新
        try:
            stats_data = await get_realtime_stats()
            await websocket_manager.broadcast_json({
                "type": "stats_update",
                "data": stats_data,
                "timestamp": datetime.now().isoformat()
            })

            # 推送新日志记录
            await websocket_manager.broadcast_json({
                "type": "new_log",
                "data": {
                    "timestamp": log_entry.timestamp,
                    "customer_id": log_entry.customer_id,
                    "api_endpoint": log_entry.api_endpoint,
                    "request_method": log_entry.request_method,
                    "response_status": log_entry.response_status,
                    "response_time": log_entry.response_time,
                    "is_suspicious": is_suspicious,
                    "risk_score": risk_score
                },
                "timestamp": datetime.now().isoformat()
            })

            logger.info(f"实时数据推送成功 - 客户: {log_entry.customer_id}, 端点: {log_entry.api_endpoint}")
        except Exception as e:
            logger.error(f"WebSocket推送失败: {str(e)}")

        return {
            "success": True,
            "message": "客户日志记录成功",
            "is_suspicious": is_suspicious,
            "risk_score": risk_score
        }

    except Exception as e:
        logger.error(f"记录客户日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"记录客户日志失败: {str(e)}")

@app.get("/api/logs")
async def get_logs(
    limit: int = 100,
    customer_id: Optional[str] = None,
    token: str = Depends(verify_token)
):
    """获取日志记录"""
    try:
        logs = db.get_logs(limit=limit, customer_id=customer_id)
        return {
            "success": True,
            "data": logs,
            "total": len(logs)
        }

    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")


@app.get("/api/logs/detailed")
async def get_detailed_logs(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    status_filter: Optional[str] = None,
    risk_filter: Optional[str] = None,
    token: str = Depends(verify_token)
):
    """获取详细日志记录，支持搜索和过滤"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if search:
            where_conditions.append("(customer_id LIKE ? OR api_endpoint LIKE ? OR client_ip LIKE ?)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        if status_filter:
            if status_filter == "success":
                where_conditions.append("response_status < 400")
            elif status_filter == "error":
                where_conditions.append("response_status >= 400")

        if risk_filter:
            if risk_filter == "high":
                where_conditions.append("risk_score >= 0.7")
            elif risk_filter == "medium":
                where_conditions.append("risk_score >= 0.3 AND risk_score < 0.7")
            elif risk_filter == "low":
                where_conditions.append("risk_score < 0.3")

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询详细日志
        query = f"""
            SELECT id, timestamp, client_ip, customer_id, user_type, api_endpoint,
                   request_method, request_headers, request_body, response_status,
                   response_body, response_time, user_agent, is_suspicious, risk_score
            FROM api_logs
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        """

        cursor.execute(query, params + [limit, offset])
        logs = cursor.fetchall()

        # 获取总数
        count_query = f"SELECT COUNT(*) FROM api_logs {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        conn.close()

        # 格式化结果
        log_list = []
        for log in logs:
            # 解析请求头
            try:
                headers = json.loads(log[7]) if log[7] else {}
            except:
                headers = {}

            log_list.append({
                "id": log[0],
                "timestamp": log[1],
                "client_ip": log[2],
                "customer_id": log[3],
                "user_type": log[4],
                "api_endpoint": log[5],
                "request_method": log[6],
                "request_headers": headers,
                "request_body": log[8][:500] if log[8] else "",
                "response_status": log[9],
                "response_body": log[10][:500] if log[10] else "",
                "response_time": log[11],
                "user_agent": log[12],
                "is_suspicious": bool(log[13]),
                "risk_score": log[14]
            })

        return {
            "success": True,
            "data": log_list,
            "total": total,
            "limit": limit,
            "offset": offset,
            "filters": {
                "search": search,
                "status_filter": status_filter,
                "risk_filter": risk_filter
            }
        }

    except Exception as e:
        logger.error(f"获取详细日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取详细日志失败: {str(e)}")

@app.get("/api/stats/customers")
async def get_customer_stats(token: str = Depends(verify_token)):
    """获取客户统计信息"""
    try:
        customer_stats = db.get_customer_stats()

        # 确保customer_stats是列表
        if not isinstance(customer_stats, list):
            customer_stats = []

        # 计算汇总统计
        total_requests = 0
        total_errors = 0

        for stat in customer_stats:
            if isinstance(stat, dict):
                total_requests += stat.get("total_requests", 0)
                total_errors += stat.get("total_errors", 0)

        active_customers = len(customer_stats)

        # 获取最近的日志来计算可疑请求数
        try:
            recent_logs = db.get_logs(limit=1000)
            if not isinstance(recent_logs, list):
                recent_logs = []
        except:
            recent_logs = []

        suspicious_requests = 0
        response_times = []

        for log in recent_logs:
            if isinstance(log, dict):
                if log.get("is_suspicious", False):
                    suspicious_requests += 1
                if log.get("response_time"):
                    response_times.append(log.get("response_time", 0))

        # 计算平均响应时间
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        logger.info(f"客户统计查询完成 - 总请求: {total_requests}, 活跃客户: {active_customers}, 可疑请求: {suspicious_requests}")

        return {
            "success": True,
            "total_requests": total_requests,
            "active_customers": active_customers,
            "suspicious_requests": suspicious_requests,
            "avg_response_time": round(avg_response_time * 1000, 2),  # 转换为毫秒
            "total_errors": total_errors,
            "data": customer_stats,
            "total": len(customer_stats)
        }

    except Exception as e:
        logger.error(f"获取客户统计失败: {str(e)}")
        # 返回默认值而不是抛出异常
        return {
            "success": True,
            "total_requests": 0,
            "active_customers": 0,
            "suspicious_requests": 0,
            "avg_response_time": 0,
            "total_errors": 0,
            "data": [],
            "total": 0
        }

@app.get("/api/dashboard/stats")
async def get_dashboard_stats(username: str = Depends(verify_web_token)):
    """获取仪表板统计数据"""
    try:
        # 获取客户统计
        customer_stats = db.get_customer_stats()

        # 获取最近的日志
        recent_logs = db.get_logs(limit=1000)

        # 计算基础统计
        total_requests = sum(stat.get("total_requests", 0) for stat in customer_stats)
        total_errors = sum(stat.get("total_errors", 0) for stat in customer_stats)
        active_customers = len(customer_stats)
        suspicious_requests = sum(1 for log in recent_logs if log.get("is_suspicious", False))

        # 计算平均响应时间
        response_times = [log.get("response_time", 0) for log in recent_logs if log.get("response_time")]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        # 计算成功率
        success_rate = ((total_requests - total_errors) / total_requests * 100) if total_requests > 0 else 100

        # 计算今日请求数
        from datetime import datetime, timedelta
        today = datetime.now().date()
        today_logs = [log for log in recent_logs if log.get("timestamp", "").startswith(str(today))]
        today_requests = len(today_logs)

        # 计算增长率（模拟数据）
        requests_growth = "+12.5%" if total_requests > 0 else "0%"
        customers_growth = "+8.2%" if active_customers > 0 else "0%"
        errors_growth = "+3.1%" if total_errors > 0 else "0%"
        response_time_growth = "+16.3%" if avg_response_time > 0 else "0%"

        return {
            "success": True,
            "stats": {
                "total_requests": {
                    "value": total_requests,
                    "growth": requests_growth,
                    "label": "总请求数"
                },
                "active_customers": {
                    "value": active_customers,
                    "growth": customers_growth,
                    "label": "活跃客户"
                },
                "total_errors": {
                    "value": total_errors,
                    "growth": errors_growth,
                    "label": "错误请求"
                },
                "avg_response_time": {
                    "value": f"{round(avg_response_time * 1000, 1)}ms",
                    "growth": response_time_growth,
                    "label": "平均响应时间"
                },
                "success_rate": round(success_rate, 1),
                "suspicious_requests": suspicious_requests,
                "today_requests": today_requests
            }
        }

    except Exception as e:
        logger.error(f"获取仪表板统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取仪表板统计失败: {str(e)}")

@app.get("/api/stats/trend")
async def get_request_trend(period: str = "24h", token: str = Depends(verify_token)):
    """获取请求趋势数据"""
    try:
        # 根据时间段计算数据点
        if period == "1h":
            hours = 1
            interval_minutes = 5
        elif period == "6h":
            hours = 6
            interval_minutes = 30
        elif period == "24h":
            hours = 24
            interval_minutes = 60
        elif period == "7d":
            hours = 24 * 7
            interval_minutes = 60 * 6
        else:
            hours = 24
            interval_minutes = 60

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 获取时间段内的日志数据
        all_logs = db.get_logs(limit=10000)
        logs = []
        for log in all_logs:
            if isinstance(log, dict) and 'timestamp' in log:
                try:
                    log_time = datetime.fromisoformat(log['timestamp'])
                    if start_time <= log_time <= end_time:
                        logs.append(log)
                except:
                    continue

        # 生成时间标签和数据点
        labels = []
        data = []
        current_time = start_time

        while current_time <= end_time:
            # 计算当前时间段的请求数
            next_time = current_time + timedelta(minutes=interval_minutes)

            count = 0
            for log in logs:
                log_time = datetime.fromisoformat(log.get('timestamp', ''))
                if current_time <= log_time < next_time:
                    count += 1

            # 格式化时间标签
            if hours <= 24:
                label = current_time.strftime('%H:%M')
            else:
                label = current_time.strftime('%m-%d %H:%M')

            labels.append(label)
            data.append(count)
            current_time = next_time

        return {
            "success": True,
            "labels": labels,
            "data": data,
            "period": period
        }

    except Exception as e:
        logger.error(f"获取请求趋势失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "labels": [],
            "data": []
        }

@app.get("/api/stats/request-types")
async def get_request_types(token: str = Depends(verify_token)):
    """获取请求类型分布数据"""
    try:
        # 获取最近的日志数据
        recent_logs = db.get_logs(limit=1000)

        normal_requests = 0
        suspicious_requests = 0
        error_requests = 0

        for log in recent_logs:
            if isinstance(log, dict):
                if log.get('is_suspicious', False):
                    suspicious_requests += 1
                elif log.get('response_status', 200) >= 400:
                    error_requests += 1
                else:
                    normal_requests += 1

        return {
            "success": True,
            "normal_requests": normal_requests,
            "suspicious_requests": suspicious_requests,
            "error_requests": error_requests,
            "total_requests": normal_requests + suspicious_requests + error_requests
        }

    except Exception as e:
        logger.error(f"获取请求类型分布失败: {str(e)}")
        return {
            "success": False,
            "normal_requests": 0,
            "suspicious_requests": 0,
            "error_requests": 0,
            "total_requests": 0
        }

@app.get("/api/system/status")
async def get_system_status(username: str = Depends(verify_web_token)):
    """获取系统状态信息"""
    try:
        import psutil
        import time

        # 获取CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)

        # 获取内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent

        # 获取磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = (disk.used / disk.total) * 100

        # 获取系统运行时间
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)
        uptime = f"{uptime_hours}小时{uptime_minutes}分钟"

        return {
            "success": True,
            "cpu_usage": round(cpu_usage, 1),
            "memory_usage": round(memory_usage, 1),
            "disk_usage": round(disk_usage, 1),
            "uptime": uptime,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@app.post("/api/cleanup")
async def cleanup_data(
    cleanup_request: CleanupRequest,
    token: str = Depends(verify_token)
):
    """清理数据 - 即用即删功能"""
    try:
        if not cleanup_request.confirm_deletion:
            raise HTTPException(status_code=400, detail="必须确认删除操作")
        
        result = db.cleanup_data(customer_id=cleanup_request.customer_id)
        
        logger.info(f"数据清理完成 - 客户: {cleanup_request.customer_id or '全部'}, 结果: {result}")
        
        return {
            "success": True,
            "message": "数据清理完成",
            "result": result
        }
    
    except Exception as e:
        logger.error(f"数据清理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")

@app.post("/api/system/reset")
async def system_reset(token: str = Depends(verify_token)):
    """系统重置 - 彻底清理所有数据"""
    try:
        # 清理数据库
        result = db.cleanup_data()
        
        # 清理日志文件
        log_files = ['./logs/monitor_backend.log']
        for log_file in log_files:
            if os.path.exists(log_file):
                os.remove(log_file)
        
        logger.info("系统重置完成")
        
        return {
            "success": True,
            "message": "系统重置完成",
            "result": result
        }
    
    except Exception as e:
        logger.error(f"系统重置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统重置失败: {str(e)}")

@app.get("/api/alerts/anomalies")
async def get_anomalies(token: str = Depends(verify_token)):
    """获取异常报告"""
    try:
        # 获取可疑日志
        logs = db.get_logs(limit=1000)
        suspicious_logs = [log for log in logs if log.get('is_suspicious', False)]
        
        # 统计异常情况
        anomalies = {
            "total_suspicious_requests": len(suspicious_logs),
            "high_risk_customers": [],
            "suspicious_patterns": {},
            "recent_alerts": suspicious_logs[:10]
        }
        
        # 分析高风险客户
        customer_risks = {}
        for log in suspicious_logs:
            customer_id = log.get('customer_id', 'unknown')
            if customer_id not in customer_risks:
                customer_risks[customer_id] = []
            customer_risks[customer_id].append(log.get('risk_score', 0))
        
        for customer_id, risks in customer_risks.items():
            avg_risk = sum(risks) / len(risks)
            if avg_risk > 0.5:
                anomalies["high_risk_customers"].append({
                    "customer_id": customer_id,
                    "average_risk_score": avg_risk,
                    "suspicious_requests": len(risks)
                })
        
        return {
            "success": True,
            "data": anomalies
        }
    
    except Exception as e:
        logger.error(f"获取异常报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取异常报告失败: {str(e)}")


@app.get("/api/customers/detailed")
async def get_detailed_customers(
    limit: int = 50,
    offset: int = 0,
    search: Optional[str] = None,
    risk_level: Optional[str] = None,
    token: str = Depends(verify_token)
):
    """获取详细客户信息"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if search:
            where_conditions.append("customer_id LIKE ?")
            params.append(f"%{search}%")

        if risk_level:
            where_conditions.append("risk_level = ?")
            params.append(risk_level.upper())

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询客户统计
        query = f"""
            SELECT customer_id, total_requests, total_errors, last_access,
                   risk_level, is_blocked
            FROM customer_stats
            {where_clause}
            ORDER BY last_access DESC
            LIMIT ? OFFSET ?
        """

        cursor.execute(query, params + [limit, offset])
        customers = cursor.fetchall()

        # 获取总数
        count_query = f"SELECT COUNT(*) FROM customer_stats {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # 为每个客户获取最近活动
        detailed_customers = []
        for customer in customers:
            customer_id = customer[0]

            # 获取最近10条日志
            cursor.execute("""
                SELECT timestamp, api_endpoint, request_method, response_status, response_time
                FROM api_logs
                WHERE customer_id = ?
                ORDER BY timestamp DESC
                LIMIT 10
            """, (customer_id,))
            recent_logs = cursor.fetchall()

            # 计算统计信息
            success_rate = 0
            if customer[1] > 0:  # total_requests
                success_rate = ((customer[1] - customer[2]) / customer[1]) * 100

            detailed_customers.append({
                "customer_id": customer_id,
                "total_requests": customer[1],
                "total_errors": customer[2],
                "last_access": customer[3],
                "risk_level": customer[4],
                "is_blocked": bool(customer[5]),
                "success_rate": round(success_rate, 2),
                "recent_activity": [
                    {
                        "timestamp": log[0],
                        "endpoint": log[1],
                        "method": log[2],
                        "status": log[3],
                        "response_time": log[4]
                    } for log in recent_logs
                ]
            })

        conn.close()

        return {
            "success": True,
            "data": detailed_customers,
            "total": total,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"获取详细客户信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取详细客户信息失败: {str(e)}")


@app.get("/api/security/alerts")
async def get_security_alerts(
    limit: int = 100,
    offset: int = 0,
    severity: Optional[str] = None,
    token: str = Depends(verify_token)
):
    """获取安全告警"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if severity:
            where_conditions.append("severity = ?")
            params.append(severity.upper())

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询告警
        query = f"""
            SELECT id, timestamp, alert_type, customer_id, message, severity, is_resolved
            FROM alerts
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        """

        cursor.execute(query, params + [limit, offset])
        alerts = cursor.fetchall()

        # 获取总数
        count_query = f"SELECT COUNT(*) FROM alerts {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # 获取统计信息
        cursor.execute("SELECT severity, COUNT(*) FROM alerts WHERE is_resolved = 0 GROUP BY severity")
        severity_stats = dict(cursor.fetchall())

        conn.close()

        # 格式化结果
        alert_list = []
        for alert in alerts:
            alert_list.append({
                "id": alert[0],
                "timestamp": alert[1],
                "alert_type": alert[2],
                "customer_id": alert[3],
                "message": alert[4],
                "severity": alert[5],
                "is_resolved": bool(alert[6])
            })

        return {
            "success": True,
            "data": alert_list,
            "total": total,
            "limit": limit,
            "offset": offset,
            "severity_stats": severity_stats
        }

    except Exception as e:
        logger.error(f"获取安全告警失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取安全告警失败: {str(e)}")


@app.get("/api/analytics/trends")
async def get_analytics_trends(
    period: str = "24h",
    metric: str = "requests",
    token: str = Depends(verify_token)
):
    """获取数据分析趋势"""
    try:
        # 根据时间段计算数据点
        if period == "1h":
            hours = 1
            interval_minutes = 5
        elif period == "6h":
            hours = 6
            interval_minutes = 30
        elif period == "24h":
            hours = 24
            interval_minutes = 60
        elif period == "7d":
            hours = 24 * 7
            interval_minutes = 60 * 6
        else:
            hours = 24
            interval_minutes = 60

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 获取数据
        all_logs = db.get_logs(limit=10000)

        # 按时间间隔分组数据
        data_points = []
        current_time = start_time

        while current_time <= end_time:
            next_time = current_time + timedelta(minutes=interval_minutes)

            # 统计当前时间段的数据
            period_logs = []
            for log in all_logs:
                if isinstance(log, dict) and 'timestamp' in log:
                    try:
                        log_time = datetime.fromisoformat(log['timestamp'])
                        if current_time <= log_time < next_time:
                            period_logs.append(log)
                    except:
                        continue

            # 根据指标类型计算值
            if metric == "requests":
                value = len(period_logs)
            elif metric == "errors":
                value = sum(1 for log in period_logs if log.get('response_status', 0) >= 400)
            elif metric == "response_time":
                times = [log.get('response_time', 0) for log in period_logs if log.get('response_time')]
                value = sum(times) / len(times) if times else 0
            elif metric == "customers":
                unique_customers = set(log.get('customer_id') for log in period_logs if log.get('customer_id'))
                value = len(unique_customers)
            else:
                value = len(period_logs)

            data_points.append({
                "timestamp": current_time.isoformat(),
                "value": round(value, 2)
            })

            current_time = next_time

        return {
            "success": True,
            "data": data_points,
            "period": period,
            "metric": metric,
            "total_points": len(data_points)
        }

    except Exception as e:
        logger.error(f"获取分析趋势失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析趋势失败: {str(e)}")


@app.get("/api/settings/config")
async def get_system_config(token: str = Depends(verify_token)):
    """获取系统配置"""
    try:
        # 获取当前配置
        config = {
            "monitoring": {
                "enabled": True,
                "log_retention_days": 30,
                "max_logs_per_customer": 10000,
                "real_time_updates": True
            },
            "security": {
                "risk_threshold_high": 0.7,
                "risk_threshold_medium": 0.3,
                "auto_block_enabled": False,
                "suspicious_patterns": [
                    "eval(",
                    "script>",
                    "union select",
                    "drop table"
                ]
            },
            "alerts": {
                "email_enabled": False,
                "webhook_enabled": False,
                "alert_threshold": 10,
                "notification_interval": 300
            },
            "performance": {
                "max_concurrent_requests": 1000,
                "request_timeout": 30,
                "database_cleanup_interval": 3600
            }
        }

        return {
            "success": True,
            "data": config
        }

    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


@app.post("/api/settings/update")
async def update_system_config(
    config_data: dict,
    token: str = Depends(verify_token)
):
    """更新系统配置"""
    try:
        # 验证配置数据
        allowed_sections = ["monitoring", "security", "alerts", "performance"]

        for section in config_data:
            if section not in allowed_sections:
                raise HTTPException(status_code=400, detail=f"不支持的配置节: {section}")

        # 这里可以将配置保存到文件或数据库
        # 目前只是验证并返回成功

        logger.info(f"系统配置更新: {config_data}")

        # 如果是安全配置，更新安全检查器
        if 'security' in config_data and 'suspicious_patterns' in config_data['security']:
            security_checker.suspicious_patterns = config_data['security']['suspicious_patterns']

        return {
            "success": True,
            "message": "配置更新成功",
            "updated_sections": list(config_data.keys())
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新系统配置失败: {str(e)}")


@app.get("/api/analytics/data")
async def get_analytics_data(
    time_range: str = "24h",
    token: str = Depends(verify_token)
):
    """获取数据分析数据"""
    try:
        # 获取日志数据
        logs = db.get_logs(limit=10000)
        customer_stats = db.get_customer_stats()

        # 计算基础指标
        total_requests = len(logs)
        error_requests = sum(1 for log in logs if log.get('response_status', 200) >= 400)
        success_rate = ((total_requests - error_requests) / total_requests * 100) if total_requests > 0 else 100

        # 计算平均响应时间
        response_times = [log.get('response_time', 0) for log in logs if log.get('response_time')]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        # 计算平均风险评分
        risk_scores = [log.get('risk_score', 0) for log in logs if log.get('risk_score')]
        avg_risk_score = sum(risk_scores) / len(risk_scores) if risk_scores else 0

        # 生成活跃客户排行
        top_customers = []
        for stat in customer_stats[:10]:  # 取前10名
            if isinstance(stat, dict):
                success_rate_customer = 100
                if stat.get('total_requests', 0) > 0:
                    success_rate_customer = ((stat.get('total_requests', 0) - stat.get('total_errors', 0)) / stat.get('total_requests', 0)) * 100

                top_customers.append({
                    'customer_id': stat.get('customer_id', ''),
                    'total_requests': stat.get('total_requests', 0),
                    'success_rate': round(success_rate_customer, 1),
                    'risk_level': stat.get('risk_level', 'LOW'),
                    'risk_score': 0.0  # 可以从日志中计算
                })

        # 按请求数排序
        top_customers.sort(key=lambda x: x['total_requests'], reverse=True)

        return {
            "success": True,
            "metrics": {
                "total_requests": total_requests,
                "success_rate": round(success_rate, 1),
                "avg_response_time": round(avg_response_time, 2),
                "avg_risk_score": round(avg_risk_score, 2)
            },
            "charts": {
                "request_trend": [],  # 可以添加时间序列数据
                "endpoint_distribution": [],  # API端点分布
                "response_time_distribution": [],  # 响应时间分布
                "error_types": []  # 错误类型分布
            },
            "top_customers": top_customers
        }

    except Exception as e:
        logger.error(f"获取分析数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析数据失败: {str(e)}")


@app.post("/api/alerts/test")
async def test_alerts(token: str = Depends(verify_token)):
    """测试告警功能"""
    try:
        # 创建测试告警
        test_alert = {
            "timestamp": datetime.now().isoformat(),
            "alert_type": "TEST",
            "customer_id": "test_customer",
            "message": "这是一个测试告警",
            "severity": "LOW"
        }

        # 记录测试告警到数据库
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO alerts (timestamp, alert_type, customer_id, message, severity)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            test_alert["timestamp"],
            test_alert["alert_type"],
            test_alert["customer_id"],
            test_alert["message"],
            test_alert["severity"]
        ))

        conn.commit()
        conn.close()

        logger.info("测试告警创建成功")

        return {
            "success": True,
            "message": "测试告警发送成功"
        }

    except Exception as e:
        logger.error(f"测试告警失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试告警失败: {str(e)}")


@app.get("/api/export/data")
async def export_data(token: str = Depends(verify_token)):
    """导出系统数据"""
    try:
        # 获取所有数据
        logs = db.get_logs(limit=None)  # 获取所有日志
        customer_stats = db.get_customer_stats()

        # 创建导出数据
        export_data_dict = {
            "export_time": datetime.now().isoformat(),
            "logs": logs,
            "customer_stats": customer_stats,
            "summary": {
                "total_logs": len(logs),
                "total_customers": len(customer_stats)
            }
        }

        # 转换为JSON字符串
        json_data = json.dumps(export_data_dict, ensure_ascii=False, indent=2)

        # 创建响应
        response = Response(
            content=json_data,
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=monitor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )

        logger.info("数据导出成功")
        return response

    except Exception as e:
        logger.error(f"数据导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据导出失败: {str(e)}")


if __name__ == "__main__":
    # 创建日志目录
    os.makedirs('./logs', exist_ok=True)
    
    # 启动监控后台
    uvicorn.run(
        app,
        host=os.getenv('MONITOR_HOST', '0.0.0.0'),
        port=int(os.getenv('MONITOR_PORT', 9000)),
        log_level="info"
    )
