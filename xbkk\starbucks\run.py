#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星巴克设备指纹风控绕过系统启动脚本
作者：YINGAshadow
创建时间：2025-7-29
功能：启动FastAPI服务器
"""

import os
import sys
import uvicorn
from src.config.settings import settings
from src.utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)


def main():
    """主函数"""
    try:
        logger.info("正在启动星巴克设备指纹风控绕过系统...")
        logger.info(f"配置信息:")
        logger.info(f"  - 监听地址: {settings.HOST}:{settings.PORT}")
        logger.info(f"  - 最大设备数: {settings.MAX_DEVICES}")
        logger.info(f"  - 调试模式: {settings.DEBUG}")
        logger.info(f"  - 日志级别: {settings.LOG_LEVEL}")
        
        # 注意：系统已升级为完全独立模式，不再依赖abcd.txt文件
        logger.info("系统运行在完全独立模式，不依赖历史样本数据")
        
        # 启动服务器
        uvicorn.run(
            "src.api.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
