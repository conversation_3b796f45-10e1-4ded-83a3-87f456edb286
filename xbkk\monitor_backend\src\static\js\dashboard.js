// 星巴克风控绕过系统 - 监控后台JavaScript

// 全局变量
let currentPage = 'overview';
let charts = {};
let websocket = null;
let refreshInterval = null;
let reconnectAttempts = 0;
let maxReconnectAttempts = 5;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuthentication();
    
    // 初始化页面
    initializePage();
    
    // 建立WebSocket连接
    connectWebSocket();

    // 初始化WebSocket状态指示器
    updateWebSocketStatus('connecting', 'WebSocket连接中');

    // 注意：不再默认启动定时刷新，WebSocket失败时会自动回退到定时刷新
});

// 检查用户认证状态
function checkAuthentication() {
    const token = localStorage.getItem('access_token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 验证token有效性
    fetch('/api/auth/verify', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    }).then(response => {
        if (!response.ok) {
            localStorage.removeItem('access_token');
            window.location.href = '/login';
        }
    }).catch(error => {
        console.error('认证验证失败:', error);
        localStorage.removeItem('access_token');
        window.location.href = '/login';
    });
}

// 初始化页面
function initializePage() {
    // 初始化图表
    initializeCharts();
    
    // 加载初始数据
    loadOverviewData();
    
    // 绑定事件监听器
    bindEventListeners();
}

// 初始化图表
function initializeCharts() {
    // 请求趋势图表
    const requestTrendCtx = document.getElementById('requestTrendChart');
    if (requestTrendCtx) {
        charts.requestTrend = new Chart(requestTrendCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '请求数量',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // 请求类型分布图表
    const requestTypeCtx = document.getElementById('requestTypeChart');
    if (requestTypeCtx) {
        charts.requestType = new Chart(requestTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['正常请求', '可疑请求', '错误请求'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: [
                        '#27ae60',
                        '#f39c12',
                        '#e74c3c'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 图表时间段控制按钮
    document.querySelectorAll('.chart-controls button').forEach(button => {
        button.addEventListener('click', function() {
            // 移除其他按钮的active类
            this.parentElement.querySelectorAll('button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 更新图表数据
            const period = this.dataset.period;
            updateRequestTrendChart(period);
        });
    });
}

// 页面切换
function showPage(pageName) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.remove('active');
    });
    
    // 移除所有菜单项的active类
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageName + '-page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 激活对应菜单项
    const targetMenuItem = document.querySelector(`[data-page="${pageName}"]`);
    if (targetMenuItem) {
        targetMenuItem.classList.add('active');
    }
    
    // 更新当前页面
    currentPage = pageName;
    
    // 根据页面加载对应数据
    loadPageData(pageName);
}

// 加载页面数据
function loadPageData(pageName) {
    switch(pageName) {
        case 'overview':
            loadOverviewData();
            break;
        case 'logs':
            loadLogsData();
            break;
        case 'customers':
            loadCustomersData();
            break;
        case 'security':
            loadSecurityData();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
        case 'settings':
            loadSettingsData();
            break;
    }
}

// 加载概览数据
function loadOverviewData() {
    console.log('开始加载概览数据...');

    // 加载统计数据
    loadStatsData();

    // 加载系统状态
    loadSystemStatus();

    // 加载最近活动
    loadRecentActivity();

    // 如果存在实时日志容器，也加载实时日志
    if (document.getElementById('realtimeLogs')) {
        loadRealtimeLogs();
    }

    // 更新图表
    updateRequestTrendChart('1h');
    updateRequestTypeChart();

    console.log('概览数据加载完成');
}

// 加载统计数据
function loadStatsData() {
    const token = localStorage.getItem('access_token');

    fetch('/api/stats/customers', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 更新统计卡片
            const totalRequests = data.total_requests || 0;
            const activeCustomers = data.active_customers || 0;
            const suspiciousRequests = data.suspicious_requests || 0;
            const avgResponseTime = data.avg_response_time || 0;

            document.getElementById('totalRequests').textContent = totalRequests;
            document.getElementById('activeCustomers').textContent = activeCustomers;
            document.getElementById('suspiciousRequests').textContent = suspiciousRequests;
            document.getElementById('avgResponseTime').textContent = avgResponseTime + 'ms';

            console.log('统计数据更新成功:', {
                totalRequests, activeCustomers, suspiciousRequests, avgResponseTime
            });
        } else {
            console.error('统计数据返回失败:', data);
        }
    })
    .catch(error => {
        console.error('加载统计数据失败:', error);
        // 显示错误状态
        document.getElementById('totalRequests').textContent = '错误';
        document.getElementById('activeCustomers').textContent = '错误';
        document.getElementById('suspiciousRequests').textContent = '错误';
        document.getElementById('avgResponseTime').textContent = '错误';
    });
}

// 加载系统状态
function loadSystemStatus() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/system/status', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新CPU使用率
            const cpuUsage = data.cpu_usage || 0;
            document.getElementById('cpuUsage').style.width = cpuUsage + '%';
            document.getElementById('cpuValue').textContent = cpuUsage.toFixed(1) + '%';
            
            // 更新内存使用率
            const memoryUsage = data.memory_usage || 0;
            document.getElementById('memoryUsage').style.width = memoryUsage + '%';
            document.getElementById('memoryValue').textContent = memoryUsage.toFixed(1) + '%';
            
            // 更新磁盘使用率
            const diskUsage = data.disk_usage || 0;
            document.getElementById('diskUsage').style.width = diskUsage + '%';
            document.getElementById('diskValue').textContent = diskUsage.toFixed(1) + '%';
        }
    })
    .catch(error => {
        console.error('加载系统状态失败:', error);
    });
}

// 加载最近活动
function loadRecentActivity() {
    const token = localStorage.getItem('access_token');

    fetch('/api/logs?limit=10', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const activityContainer = document.getElementById('recentActivity');
            activityContainer.innerHTML = '';

            // 使用正确的数据字段
            const logs = data.data || data.logs || [];

            if (logs.length > 0) {
                logs.forEach(log => {
                    const activityItem = createActivityItem(log);
                    activityContainer.appendChild(activityItem);
                });
                console.log(`最近活动更新成功，加载了 ${logs.length} 条记录`);
            } else {
                activityContainer.innerHTML = '<div class="no-data">暂无活动记录</div>';
            }
        } else {
            console.error('最近活动返回失败:', data);
            document.getElementById('recentActivity').innerHTML = '<div class="error">加载活动失败</div>';
        }
    })
    .catch(error => {
        console.error('加载最近活动失败:', error);
        document.getElementById('recentActivity').innerHTML = '<div class="error">网络错误，无法加载活动</div>';
    });
}

// 加载实时日志
function loadRealtimeLogs() {
    const token = localStorage.getItem('access_token');

    fetch('/api/logs?limit=50', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const logsContainer = document.getElementById('realtimeLogs');
            if (!logsContainer) return;

            logsContainer.innerHTML = '';

            const logs = data.data || [];

            if (logs.length > 0) {
                logs.forEach(log => {
                    const logElement = document.createElement('div');
                    logElement.className = 'log-entry';

                    const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
                    const statusClass = log.response_status >= 400 ? 'error' : 'success';
                    const suspiciousClass = log.is_suspicious ? 'suspicious' : '';

                    logElement.innerHTML = `
                        <div class="log-time">${timestamp}</div>
                        <div class="log-content ${suspiciousClass}">
                            <span class="log-customer">客户: ${log.customer_id || '未知'}</span>
                            <span class="log-endpoint">端点: ${log.api_endpoint || '未知'}</span>
                            <span class="log-method">方法: ${log.request_method || 'GET'}</span>
                            <span class="log-status status-${statusClass}">状态: ${log.response_status || 0}</span>
                            <span class="log-time-ms">响应: ${((log.response_time || 0) * 1000).toFixed(2)}ms</span>
                            ${log.is_suspicious ? '<span class="log-suspicious">可疑</span>' : ''}
                        </div>
                    `;
                    logsContainer.appendChild(logElement);
                });

                console.log(`实时日志更新成功，加载了 ${logs.length} 条日志`);
            } else {
                logsContainer.innerHTML = '<div class="no-data">暂无日志数据</div>';
            }
        } else {
            console.error('实时日志返回失败:', data);
        }
    })
    .catch(error => {
        console.error('加载实时日志失败:', error);
        const logsContainer = document.getElementById('realtimeLogs');
        if (logsContainer) {
            logsContainer.innerHTML = '<div class="error">网络错误，无法加载日志</div>';
        }
    });
}

// 创建活动项目元素
function createActivityItem(log) {
    const item = document.createElement('div');
    item.className = 'activity-item';

    const statusClass = log.is_suspicious ? 'text-warning' :
                       (log.response_status >= 400 ? 'text-danger' : 'text-success');

    item.innerHTML = `
        <div class="activity-icon ${statusClass}">
            <i class="bi bi-${log.is_suspicious ? 'exclamation-triangle' :
                              (log.response_status >= 400 ? 'x-circle' : 'check-circle')}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${log.api_endpoint || '未知端点'}</div>
            <div class="activity-meta">
                <span class="activity-customer">${log.customer_id || '未知客户'}</span>
                <span class="activity-time">${formatTime(log.timestamp)}</span>
                <span class="activity-status ${statusClass}">${log.response_status || 0}</span>
            </div>
        </div>
    `;

    return item;
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 小于1分钟
        return '刚刚';
    } else if (diff < 3600000) { // 小于1小时
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 小于1天
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString();
    }
}

// 更新请求趋势图表
function updateRequestTrendChart(period) {
    const token = localStorage.getItem('access_token');

    fetch(`/api/stats/trend?period=${period}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && charts.requestTrend) {
            const labels = data.labels || [];
            const values = data.data || [];

            charts.requestTrend.data.labels = labels;
            charts.requestTrend.data.datasets[0].data = values;
            charts.requestTrend.update();

            console.log(`请求趋势图表更新成功，时间段: ${period}`);
        }
    })
    .catch(error => {
        console.error('更新请求趋势图表失败:', error);
        // 如果API失败，使用基础数据
        if (charts.requestTrend) {
            const fallbackData = generateMockTrendData(period);
            charts.requestTrend.data.labels = fallbackData.labels;
            charts.requestTrend.data.datasets[0].data = fallbackData.data;
            charts.requestTrend.update();
        }
    });
}

// 更新请求类型图表
function updateRequestTypeChart() {
    const token = localStorage.getItem('access_token');

    fetch('/api/stats/request-types', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && charts.requestType) {
            const typeData = [
                data.normal_requests || 0,
                data.suspicious_requests || 0,
                data.error_requests || 0
            ];

            charts.requestType.data.datasets[0].data = typeData;
            charts.requestType.update();

            console.log('请求类型图表更新成功');
        }
    })
    .catch(error => {
        console.error('更新请求类型图表失败:', error);
        // 如果API失败，使用当前统计数据
        if (charts.requestType) {
            const totalRequests = parseInt(document.getElementById('totalRequests').textContent) || 0;
            const suspiciousRequests = parseInt(document.getElementById('suspiciousRequests').textContent) || 0;
            const normalRequests = totalRequests - suspiciousRequests;
            const errorRequests = Math.floor(totalRequests * 0.05); // 估算5%错误率

            charts.requestType.data.datasets[0].data = [normalRequests, suspiciousRequests, errorRequests];
            charts.requestType.update();
        }
    });
}

// 生成模拟趋势数据
function generateMockTrendData(period) {
    const now = new Date();
    const labels = [];
    const data = [];
    
    let points, interval;
    switch(period) {
        case '1h':
            points = 12;
            interval = 5 * 60 * 1000; // 5分钟
            break;
        case '6h':
            points = 24;
            interval = 15 * 60 * 1000; // 15分钟
            break;
        case '24h':
            points = 24;
            interval = 60 * 60 * 1000; // 1小时
            break;
        default:
            points = 12;
            interval = 5 * 60 * 1000;
    }
    
    for (let i = points - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval);
        labels.push(time.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        }));
        data.push(Math.floor(Math.random() * 100) + 20);
    }
    
    return { labels, data };
}

// WebSocket连接管理
function connectWebSocket() {
    try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/monitor`;

        websocket = new WebSocket(wsUrl);

        websocket.onopen = function(event) {
            console.log('WebSocket连接已建立');
            reconnectAttempts = 0;

            // 更新状态指示器
            updateWebSocketStatus('connected', 'WebSocket已连接');

            // 发送心跳包
            setInterval(() => {
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({
                        type: 'ping',
                        timestamp: new Date().toISOString()
                    }));
                }
            }, 30000);
        };

        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        websocket.onclose = function(event) {
            console.log('WebSocket连接已关闭');
            websocket = null;

            // 更新状态指示器
            updateWebSocketStatus('disconnected', 'WebSocket已断开');

            // 尝试重连
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                updateWebSocketStatus('connecting', `重连中 (${reconnectAttempts}/${maxReconnectAttempts})`);
                console.log(`尝试重连WebSocket (${reconnectAttempts}/${maxReconnectAttempts})`);
                setTimeout(connectWebSocket, 5000);
            } else {
                console.error('WebSocket重连失败，已达到最大重试次数');
                updateWebSocketStatus('disconnected', 'WebSocket连接失败');
                // 回退到定时刷新
                startAutoRefresh();
            }
        };

        websocket.onerror = function(error) {
            console.error('WebSocket连接错误:', error);
        };

    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        // 回退到定时刷新
        startAutoRefresh();
    }
}

function handleWebSocketMessage(data) {
    console.log('收到WebSocket消息:', data);

    switch (data.type) {
        case 'connection':
            console.log('WebSocket连接确认:', data.message);
            break;

        case 'pong':
            // 心跳响应，无需处理
            break;

        case 'stats_update':
            updateStatsDisplay(data.data);
            break;

        case 'new_log':
            handleNewLogEntry(data.data);
            break;

        default:
            console.log('未知的WebSocket消息类型:', data.type);
    }
}

function updateStatsDisplay(stats) {
    // 更新统计卡片
    if (document.getElementById('totalRequests')) {
        document.getElementById('totalRequests').textContent = stats.total_requests || 0;
    }
    if (document.getElementById('activeCustomers')) {
        document.getElementById('activeCustomers').textContent = stats.active_customers || 0;
    }
    if (document.getElementById('suspiciousRequests')) {
        document.getElementById('suspiciousRequests').textContent = stats.suspicious_requests || 0;
    }
    if (document.getElementById('avgResponseTime')) {
        document.getElementById('avgResponseTime').textContent = (stats.avg_response_time || 0) + 'ms';
    }

    console.log('统计数据已更新:', stats);
}

function handleNewLogEntry(logData) {
    // 在实时日志区域添加新日志
    const logsContainer = document.getElementById('realtimeLogs');
    if (logsContainer) {
        const logElement = document.createElement('div');
        logElement.className = 'log-entry';
        if (logData.is_suspicious) {
            logElement.className += ' suspicious';
        }

        logElement.innerHTML = `
            <div class="log-time">${new Date(logData.timestamp).toLocaleString()}</div>
            <div class="log-content">
                <span class="customer-id">${logData.customer_id}</span>
                <span class="api-endpoint">${logData.api_endpoint}</span>
                <span class="status-${logData.response_status >= 400 ? 'error' : 'success'}">${logData.response_status}</span>
                <span class="response-time">${logData.response_time}ms</span>
                ${logData.is_suspicious ? '<span class="suspicious-flag">可疑</span>' : ''}
            </div>
        `;

        // 插入到顶部
        logsContainer.insertBefore(logElement, logsContainer.firstChild);

        // 限制显示的日志数量
        const logEntries = logsContainer.querySelectorAll('.log-entry');
        if (logEntries.length > 50) {
            logsContainer.removeChild(logEntries[logEntries.length - 1]);
        }
    }

    console.log('新日志已添加:', logData);
}

function updateWebSocketStatus(status, message) {
    const statusElement = document.getElementById('websocketStatus');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `badge bg-${status === 'connected' ? 'success' : status === 'connecting' ? 'warning' : 'danger'} me-2`;
        statusElement.classList.add(status);
    }
}

// 开始自动刷新
function startAutoRefresh() {
    // 每10秒刷新一次数据，提高实时性
    refreshInterval = setInterval(() => {
        console.log('自动刷新数据...');
        if (currentPage === 'overview') {
            loadOverviewData();
        }
    }, 10000);

    console.log('自动刷新已启动，每10秒刷新一次');
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// 刷新活动
function refreshActivity() {
    loadRecentActivity();
}

// 显示系统信息
function showSystemInfo() {
    // 更新系统运行时间
    updateSystemUptime();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
    modal.show();
}

// 更新系统运行时间
function updateSystemUptime() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/system/status', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('systemUptime').textContent = data.uptime || '未知';
            document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
        }
    })
    .catch(error => {
        console.error('获取系统信息失败:', error);
        document.getElementById('systemUptime').textContent = '获取失败';
        document.getElementById('serverTime').textContent = new Date().toLocaleString('zh-CN');
    });
}

// 用户登出
function logout() {
    const token = localStorage.getItem('access_token');
    
    fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(() => {
        localStorage.removeItem('access_token');
        localStorage.removeItem('remember_login');
        window.location.href = '/login';
    })
    .catch(error => {
        console.error('登出失败:', error);
        // 即使登出请求失败，也清除本地token
        localStorage.removeItem('access_token');
        localStorage.removeItem('remember_login');
        window.location.href = '/login';
    });
}

// 加载日志页面数据
function loadLogsData() {
    console.log('加载日志数据');

    const logsPage = document.getElementById('logs-page');
    if (!logsPage) return;

    // 创建日志页面内容
    logsPage.innerHTML = `
        <div class="page-header">
            <h2><i class="bi bi-journal-text me-2"></i>实时日志</h2>
            <p class="text-muted">查看系统实时日志和客户访问记录</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <input type="text" class="form-control" id="logSearch" placeholder="搜索客户ID、IP或端点">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="success">成功</option>
                    <option value="error">错误</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="riskFilter">
                    <option value="">全部风险</option>
                    <option value="high">高风险</option>
                    <option value="medium">中风险</option>
                    <option value="low">低风险</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary" onclick="searchLogs()">搜索</button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary" onclick="refreshLogs()">刷新</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">日志记录</h5>
                <span class="badge bg-primary" id="logsCount">0</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>时间</th>
                                <th>客户ID</th>
                                <th>IP地址</th>
                                <th>端点</th>
                                <th>方法</th>
                                <th>状态</th>
                                <th>响应时间</th>
                                <th>风险</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <nav>
                    <ul class="pagination pagination-sm mb-0" id="logsPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    `;

    // 加载日志数据
    loadDetailedLogs();
}

function loadDetailedLogs(page = 1, limit = 50) {
    const token = localStorage.getItem('access_token');
    const offset = (page - 1) * limit;

    // 获取搜索条件
    const search = document.getElementById('logSearch')?.value || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const riskFilter = document.getElementById('riskFilter')?.value || '';

    let url = `/api/logs/detailed?limit=${limit}&offset=${offset}`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    if (statusFilter) url += `&status_filter=${statusFilter}`;
    if (riskFilter) url += `&risk_filter=${riskFilter}`;

    fetch(url, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络错误');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            displayLogs(data.data);
            updateLogsPagination(data.total, page, limit);
            document.getElementById('logsCount').textContent = data.total;
        } else {
            throw new Error(data.message || '获取日志失败');
        }
    })
    .catch(error => {
        console.error('加载详细日志失败:', error);
        document.getElementById('logsTableBody').innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-danger py-4">
                    加载失败: ${error.message}
                </td>
            </tr>
        `;
    });
}

function displayLogs(logs) {
    const tbody = document.getElementById('logsTableBody');
    if (!tbody) return;

    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted py-4">
                    暂无日志记录
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = logs.map(log => {
        const timestamp = new Date(log.timestamp).toLocaleString('zh-CN');
        const statusClass = log.response_status >= 400 ? 'text-danger' : 'text-success';
        const riskClass = log.risk_score >= 0.7 ? 'danger' : log.risk_score >= 0.3 ? 'warning' : 'success';
        const responseTime = ((log.response_time || 0) * 1000).toFixed(2);

        return `
            <tr>
                <td><small>${timestamp}</small></td>
                <td><code>${log.customer_id}</code></td>
                <td>${log.client_ip}</td>
                <td><code>${log.api_endpoint}</code></td>
                <td><span class="badge bg-secondary">${log.request_method}</span></td>
                <td><span class="badge bg-${log.response_status >= 400 ? 'danger' : 'success'}">${log.response_status}</span></td>
                <td>${responseTime}ms</td>
                <td><span class="badge bg-${riskClass}">${(log.risk_score * 100).toFixed(0)}%</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails(${log.id})">
                        详情
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function updateLogsPagination(total, currentPage, limit) {
    const pagination = document.getElementById('logsPagination');
    if (!pagination) return;

    const totalPages = Math.ceil(total / limit);
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadDetailedLogs(${currentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadDetailedLogs(${i})">${i}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadDetailedLogs(${currentPage + 1})">下一页</a></li>`;
    }

    pagination.innerHTML = paginationHTML;
}

function searchLogs() {
    loadDetailedLogs(1);
}

function refreshLogs() {
    document.getElementById('logSearch').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('riskFilter').value = '';
    loadDetailedLogs(1);
}

function viewLogDetails(logId) {
    // 这里可以实现日志详情查看功能
    alert(`查看日志详情 ID: ${logId}`);
}

// 加载客户统计页面数据
function loadCustomersData() {
    console.log('加载客户数据');

    const customersPage = document.getElementById('customers-page');
    if (!customersPage) return;

    // 创建客户页面内容
    customersPage.innerHTML = `
        <div class="page-header">
            <h2><i class="bi bi-people me-2"></i>客户统计</h2>
            <p class="text-muted">客户使用情况统计和分析</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <input type="text" class="form-control" id="customerSearch" placeholder="搜索客户ID">
            </div>
            <div class="col-md-2">
                <select class="form-select" id="riskLevelFilter">
                    <option value="">全部风险等级</option>
                    <option value="LOW">低风险</option>
                    <option value="MEDIUM">中风险</option>
                    <option value="HIGH">高风险</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary" onclick="searchCustomers()">搜索</button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary" onclick="refreshCustomers()">刷新</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">客户列表</h5>
                <span class="badge bg-primary" id="customersCount">0</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>客户ID</th>
                                <th>总请求数</th>
                                <th>错误数</th>
                                <th>成功率</th>
                                <th>风险等级</th>
                                <th>最后访问</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <nav>
                    <ul class="pagination pagination-sm mb-0" id="customersPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    `;

    // 加载客户数据
    loadDetailedCustomers();
}

function loadDetailedCustomers(page = 1, limit = 20) {
    const token = localStorage.getItem('access_token');
    const offset = (page - 1) * limit;

    // 获取搜索条件
    const search = document.getElementById('customerSearch')?.value || '';
    const riskLevel = document.getElementById('riskLevelFilter')?.value || '';

    let url = `/api/customers/detailed?limit=${limit}&offset=${offset}`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    if (riskLevel) url += `&risk_level=${riskLevel}`;

    fetch(url, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络错误');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            displayCustomers(data.data);
            updateCustomersPagination(data.total, page, limit);
            document.getElementById('customersCount').textContent = data.total;
        } else {
            throw new Error(data.message || '获取客户数据失败');
        }
    })
    .catch(error => {
        console.error('加载详细客户数据失败:', error);
        document.getElementById('customersTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-danger py-4">
                    加载失败: ${error.message}
                </td>
            </tr>
        `;
    });
}

function displayCustomers(customers) {
    const tbody = document.getElementById('customersTableBody');
    if (!tbody) return;

    if (customers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    暂无客户记录
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = customers.map(customer => {
        const lastAccess = new Date(customer.last_access).toLocaleString('zh-CN');
        const riskClass = customer.risk_level === 'HIGH' ? 'danger' : customer.risk_level === 'MEDIUM' ? 'warning' : 'success';
        const statusClass = customer.is_blocked ? 'danger' : 'success';
        const statusText = customer.is_blocked ? '已封禁' : '正常';

        return `
            <tr>
                <td><code>${customer.customer_id}</code></td>
                <td>${customer.total_requests}</td>
                <td>${customer.total_errors}</td>
                <td>${customer.success_rate}%</td>
                <td><span class="badge bg-${riskClass}">${customer.risk_level}</span></td>
                <td><small>${lastAccess}</small></td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomerDetails('${customer.customer_id}')">
                        详情
                    </button>
                    ${!customer.is_blocked ?
                        `<button class="btn btn-sm btn-outline-danger ms-1" onclick="blockCustomer('${customer.customer_id}')">封禁</button>` :
                        `<button class="btn btn-sm btn-outline-success ms-1" onclick="unblockCustomer('${customer.customer_id}')">解封</button>`
                    }
                </td>
            </tr>
        `;
    }).join('');
}

function updateCustomersPagination(total, currentPage, limit) {
    const pagination = document.getElementById('customersPagination');
    if (!pagination) return;

    const totalPages = Math.ceil(total / limit);
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadDetailedCustomers(${currentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadDetailedCustomers(${i})">${i}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadDetailedCustomers(${currentPage + 1})">下一页</a></li>`;
    }

    pagination.innerHTML = paginationHTML;
}

function searchCustomers() {
    loadDetailedCustomers(1);
}

function refreshCustomers() {
    document.getElementById('customerSearch').value = '';
    document.getElementById('riskLevelFilter').value = '';
    loadDetailedCustomers(1);
}

function viewCustomerDetails(customerId) {
    alert(`查看客户详情: ${customerId}`);
}

function blockCustomer(customerId) {
    if (confirm(`确定要封禁客户 ${customerId} 吗？`)) {
        alert(`封禁客户: ${customerId}`);
        // 这里可以调用封禁API
        refreshCustomers();
    }
}

function unblockCustomer(customerId) {
    if (confirm(`确定要解封客户 ${customerId} 吗？`)) {
        alert(`解封客户: ${customerId}`);
        // 这里可以调用解封API
        refreshCustomers();
    }
}

// 加载安全监控页面数据
function loadSecurityData() {
    console.log('加载安全数据');

    const securityPage = document.getElementById('security-page');
    if (!securityPage) return;

    // 创建安全监控页面内容
    securityPage.innerHTML = `
        <div class="page-header">
            <h2><i class="bi bi-shield-check me-2"></i>安全监控</h2>
            <p class="text-muted">系统安全告警和威胁检测</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger" id="highAlerts">0</h5>
                        <p class="card-text">高危告警</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="mediumAlerts">0</h5>
                        <p class="card-text">中危告警</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="lowAlerts">0</h5>
                        <p class="card-text">低危告警</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="resolvedAlerts">0</h5>
                        <p class="card-text">已处理</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <select class="form-select" id="severityFilter">
                    <option value="">全部严重程度</option>
                    <option value="HIGH">高危</option>
                    <option value="MEDIUM">中危</option>
                    <option value="LOW">低危</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary" onclick="searchAlerts()">筛选</button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary" onclick="refreshAlerts()">刷新</button>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">安全告警</h5>
                <span class="badge bg-danger" id="alertsCount">0</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>时间</th>
                                <th>告警类型</th>
                                <th>客户ID</th>
                                <th>严重程度</th>
                                <th>消息</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="alertsTableBody">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <nav>
                    <ul class="pagination pagination-sm mb-0" id="alertsPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    `;

    // 加载安全告警数据
    loadSecurityAlerts();
}

function loadSecurityAlerts(page = 1, limit = 50) {
    const token = localStorage.getItem('access_token');
    const offset = (page - 1) * limit;

    // 获取筛选条件
    const severity = document.getElementById('severityFilter')?.value || '';

    let url = `/api/security/alerts?limit=${limit}&offset=${offset}`;
    if (severity) url += `&severity=${severity}`;

    fetch(url, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络错误');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            displayAlerts(data.data);
            updateAlertsPagination(data.total, page, limit);
            updateAlertStats(data.severity_stats);
            document.getElementById('alertsCount').textContent = data.total;
        } else {
            throw new Error(data.message || '获取安全告警失败');
        }
    })
    .catch(error => {
        console.error('加载安全告警失败:', error);
        document.getElementById('alertsTableBody').innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-danger py-4">
                    加载失败: ${error.message}
                </td>
            </tr>
        `;
    });
}

function displayAlerts(alerts) {
    const tbody = document.getElementById('alertsTableBody');
    if (!tbody) return;

    if (alerts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    暂无安全告警
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = alerts.map(alert => {
        const timestamp = new Date(alert.timestamp).toLocaleString('zh-CN');
        const severityClass = alert.severity === 'HIGH' ? 'danger' : alert.severity === 'MEDIUM' ? 'warning' : 'info';
        const statusClass = alert.is_resolved ? 'success' : 'warning';
        const statusText = alert.is_resolved ? '已处理' : '待处理';

        return `
            <tr>
                <td><small>${timestamp}</small></td>
                <td><span class="badge bg-secondary">${alert.alert_type}</span></td>
                <td><code>${alert.customer_id}</code></td>
                <td><span class="badge bg-${severityClass}">${alert.severity}</span></td>
                <td>${alert.message}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    ${!alert.is_resolved ?
                        `<button class="btn btn-sm btn-outline-success" onclick="resolveAlert(${alert.id})">处理</button>` :
                        `<button class="btn btn-sm btn-outline-primary" onclick="viewAlertDetails(${alert.id})">详情</button>`
                    }
                </td>
            </tr>
        `;
    }).join('');
}

function updateAlertStats(severityStats) {
    document.getElementById('highAlerts').textContent = severityStats.HIGH || 0;
    document.getElementById('mediumAlerts').textContent = severityStats.MEDIUM || 0;
    document.getElementById('lowAlerts').textContent = severityStats.LOW || 0;
    // 已处理的告警数量需要单独计算
    document.getElementById('resolvedAlerts').textContent = '0';
}

function updateAlertsPagination(total, currentPage, limit) {
    const pagination = document.getElementById('alertsPagination');
    if (!pagination) return;

    const totalPages = Math.ceil(total / limit);
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadSecurityAlerts(${currentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadSecurityAlerts(${i})">${i}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadSecurityAlerts(${currentPage + 1})">下一页</a></li>`;
    }

    pagination.innerHTML = paginationHTML;
}

function searchAlerts() {
    loadSecurityAlerts(1);
}

function refreshAlerts() {
    document.getElementById('severityFilter').value = '';
    loadSecurityAlerts(1);
}

function resolveAlert(alertId) {
    if (confirm('确定要标记此告警为已处理吗？')) {
        alert(`处理告警 ID: ${alertId}`);
        // 这里可以调用处理告警的API
        refreshAlerts();
    }
}

function viewAlertDetails(alertId) {
    alert(`查看告警详情 ID: ${alertId}`);
}

// 加载数据分析页面数据
function loadAnalyticsData() {
    console.log('加载数据分析数据');

    const analyticsPage = document.getElementById('analytics-page');
    if (!analyticsPage) return;

    // 创建数据分析页面内容
    analyticsPage.innerHTML = `
        <div class="page-header">
            <h2><i class="bi bi-graph-up me-2"></i>数据分析</h2>
            <p class="text-muted">深度数据分析和业务洞察</p>
        </div>

        <!-- 时间范围选择 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <label class="form-label">时间范围</label>
                <select class="form-select" id="analyticsTimeRange">
                    <option value="1h">最近1小时</option>
                    <option value="6h">最近6小时</option>
                    <option value="24h" selected>最近24小时</option>
                    <option value="7d">最近7天</option>
                    <option value="30d">最近30天</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-primary d-block" onclick="refreshAnalytics()">刷新数据</button>
            </div>
        </div>

        <!-- 关键指标卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary" id="analyticsRequests">0</h5>
                        <p class="card-text">总请求量</p>
                        <small class="text-muted" id="requestsGrowth">+0%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="analyticsSuccessRate">0%</h5>
                        <p class="card-text">成功率</p>
                        <small class="text-muted" id="successRateGrowth">+0%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="analyticsAvgTime">0ms</h5>
                        <p class="card-text">平均响应时间</p>
                        <small class="text-muted" id="avgTimeGrowth">+0%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="analyticsRiskScore">0</h5>
                        <p class="card-text">平均风险评分</p>
                        <small class="text-muted" id="riskScoreGrowth">+0%</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>请求量趋势分析</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsRequestChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>API端点分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsEndpointChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应时间和错误分析 -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>响应时间分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsResponseTimeChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>错误类型分析</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsErrorChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户行为分析 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-people me-2"></i>活跃客户排行</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>客户ID</th>
                                        <th>请求数</th>
                                        <th>成功率</th>
                                        <th>风险评分</th>
                                    </tr>
                                </thead>
                                <tbody id="topCustomersTable">
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clock me-2"></i>访问时间分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsTimeDistChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 初始化分析图表
    initializeAnalyticsCharts();

    // 加载分析数据
    loadAnalyticsChartData();
}

// 加载系统设置页面数据
function loadSettingsData() {
    console.log('加载系统设置数据');

    const settingsPage = document.getElementById('settings-page');
    if (!settingsPage) return;

    // 创建系统设置页面内容
    settingsPage.innerHTML = `
        <div class="page-header">
            <h2><i class="bi bi-gear me-2"></i>系统设置</h2>
            <p class="text-muted">系统配置和管理设置</p>
        </div>

        <!-- 设置选项卡 -->
        <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab">
                    <i class="bi bi-activity me-2"></i>监控设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                    <i class="bi bi-shield-check me-2"></i>安全设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                    <i class="bi bi-bell me-2"></i>告警设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                    <i class="bi bi-cpu me-2"></i>系统管理
                </button>
            </li>
        </ul>

        <!-- 设置内容 -->
        <div class="tab-content" id="settingsTabContent">
            <!-- 监控设置 -->
            <div class="tab-pane fade show active" id="monitoring" role="tabpanel">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">监控配置</h5>
                            </div>
                            <div class="card-body">
                                <form id="monitoringForm">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">日志保留天数</label>
                                            <input type="number" class="form-control" id="logRetentionDays" value="30" min="1" max="365">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">每客户最大日志数</label>
                                            <input type="number" class="form-control" id="maxLogsPerCustomer" value="10000" min="100" max="100000">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="realtimeUpdates" checked>
                                                <label class="form-check-label" for="realtimeUpdates">
                                                    启用实时更新
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="detailedLogging" checked>
                                                <label class="form-check-label" for="detailedLogging">
                                                    详细日志记录
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveMonitoringSettings()">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">监控状态</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>监控服务</span>
                                    <span class="badge bg-success">运行中</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>WebSocket连接</span>
                                    <span class="badge bg-success" id="wsConnectionStatus">已连接</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>数据库状态</span>
                                    <span class="badge bg-success">正常</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>存储空间</span>
                                    <span class="badge bg-info" id="storageUsage">计算中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全设置 -->
            <div class="tab-pane fade" id="security" role="tabpanel">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">安全配置</h5>
                            </div>
                            <div class="card-body">
                                <form id="securityForm">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">高风险阈值</label>
                                            <input type="number" class="form-control" id="riskThresholdHigh" value="0.7" min="0" max="1" step="0.1">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">中风险阈值</label>
                                            <input type="number" class="form-control" id="riskThresholdMedium" value="0.3" min="0" max="1" step="0.1">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="autoBlockEnabled">
                                                <label class="form-check-label" for="autoBlockEnabled">
                                                    自动封禁高风险客户
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="ipWhitelistEnabled">
                                                <label class="form-check-label" for="ipWhitelistEnabled">
                                                    启用IP白名单
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">可疑模式列表</label>
                                        <textarea class="form-control" id="suspiciousPatterns" rows="5" placeholder="每行一个模式">eval(
script>
union select
drop table
../
<script</textarea>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">安全统计</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>今日可疑请求</span>
                                    <span class="badge bg-warning" id="todaySuspicious">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>已封禁客户</span>
                                    <span class="badge bg-danger" id="blockedCustomers">0</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>安全等级</span>
                                    <span class="badge bg-success">高</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>最后威胁检测</span>
                                    <small class="text-muted" id="lastThreatTime">无</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 加载当前设置
    loadCurrentSettings();
}

// 初始化分析图表
function initializeAnalyticsCharts() {
    // 请求量趋势图表
    const requestCtx = document.getElementById('analyticsRequestChart');
    if (requestCtx) {
        new Chart(requestCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '请求量',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // API端点分布饼图
    const endpointCtx = document.getElementById('analyticsEndpointChart');
    if (endpointCtx) {
        new Chart(endpointCtx, {
            type: 'pie',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // 响应时间分布图
    const responseTimeCtx = document.getElementById('analyticsResponseTimeChart');
    if (responseTimeCtx) {
        new Chart(responseTimeCtx, {
            type: 'bar',
            data: {
                labels: ['0-100ms', '100-500ms', '500ms-1s', '1s-5s', '5s+'],
                datasets: [{
                    label: '请求数量',
                    data: [0, 0, 0, 0, 0],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // 错误类型分析图
    const errorCtx = document.getElementById('analyticsErrorChart');
    if (errorCtx) {
        new Chart(errorCtx, {
            type: 'doughnut',
            data: {
                labels: ['4xx错误', '5xx错误', '超时', '其他'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#FF6384',
                        '#FF9F40',
                        '#FFCD56',
                        '#4BC0C0'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // 访问时间分布图
    const timeDistCtx = document.getElementById('analyticsTimeDistChart');
    if (timeDistCtx) {
        new Chart(timeDistCtx, {
            type: 'radar',
            data: {
                labels: ['0-4时', '4-8时', '8-12时', '12-16时', '16-20时', '20-24时'],
                datasets: [{
                    label: '访问量',
                    data: [0, 0, 0, 0, 0, 0],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// 加载分析图表数据
function loadAnalyticsChartData() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 获取分析数据
    fetch('/api/analytics/data', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAnalyticsMetrics(data.metrics);
            updateAnalyticsCharts(data.charts);
            updateTopCustomersTable(data.top_customers);
        }
    })
    .catch(error => {
        console.error('加载分析数据失败:', error);
    });
}

// 更新分析指标
function updateAnalyticsMetrics(metrics) {
    document.getElementById('analyticsRequests').textContent = metrics.total_requests || 0;
    document.getElementById('analyticsSuccessRate').textContent = (metrics.success_rate || 0) + '%';
    document.getElementById('analyticsAvgTime').textContent = (metrics.avg_response_time || 0) + 'ms';
    document.getElementById('analyticsRiskScore').textContent = (metrics.avg_risk_score || 0).toFixed(2);
}

// 更新分析图表
function updateAnalyticsCharts(chartData) {
    // 这里可以更新各个图表的数据
    console.log('更新图表数据:', chartData);
}

// 更新活跃客户排行表
function updateTopCustomersTable(customers) {
    const tbody = document.getElementById('topCustomersTable');
    if (!tbody || !customers) return;

    if (customers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    暂无客户数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = customers.map((customer, index) => `
        <tr>
            <td>${index + 1}</td>
            <td><code>${customer.customer_id}</code></td>
            <td>${customer.total_requests}</td>
            <td>${customer.success_rate}%</td>
            <td><span class="badge bg-${customer.risk_level === 'HIGH' ? 'danger' : customer.risk_level === 'MEDIUM' ? 'warning' : 'success'}">${customer.risk_score}</span></td>
        </tr>
    `).join('');
}

// 刷新分析数据
function refreshAnalytics() {
    loadAnalyticsChartData();
}

// 加载当前设置
function loadCurrentSettings() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/settings/config', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateSettingsForm(data.data);
        }
    })
    .catch(error => {
        console.error('加载设置失败:', error);
    });
}

// 填充设置表单
function populateSettingsForm(config) {
    // 监控设置
    if (config.monitoring) {
        document.getElementById('logRetentionDays').value = config.monitoring.log_retention_days || 30;
        document.getElementById('maxLogsPerCustomer').value = config.monitoring.max_logs_per_customer || 10000;
        document.getElementById('realtimeUpdates').checked = config.monitoring.real_time_updates !== false;
        document.getElementById('detailedLogging').checked = config.monitoring.detailed_logging !== false;
    }

    // 安全设置
    if (config.security) {
        document.getElementById('riskThresholdHigh').value = config.security.risk_threshold_high || 0.7;
        document.getElementById('riskThresholdMedium').value = config.security.risk_threshold_medium || 0.3;
        document.getElementById('autoBlockEnabled').checked = config.security.auto_block_enabled === true;
        document.getElementById('ipWhitelistEnabled').checked = config.security.ip_whitelist_enabled === true;

        if (config.security.suspicious_patterns) {
            document.getElementById('suspiciousPatterns').value = config.security.suspicious_patterns.join('\n');
        }
    }

    // 告警设置
    if (config.alerts) {
        document.getElementById('emailAlertsEnabled').checked = config.alerts.email_enabled === true;
        document.getElementById('webhookAlertsEnabled').checked = config.alerts.webhook_enabled === true;
        document.getElementById('alertThreshold').value = config.alerts.alert_threshold || 10;
        document.getElementById('notificationInterval').value = config.alerts.notification_interval || 300;
    }
}

// 保存监控设置
function saveMonitoringSettings() {
    const settings = {
        log_retention_days: parseInt(document.getElementById('logRetentionDays').value),
        max_logs_per_customer: parseInt(document.getElementById('maxLogsPerCustomer').value),
        real_time_updates: document.getElementById('realtimeUpdates').checked,
        detailed_logging: document.getElementById('detailedLogging').checked
    };

    saveSettings('monitoring', settings);
}

// 保存安全设置
function saveSecuritySettings() {
    const patterns = document.getElementById('suspiciousPatterns').value
        .split('\n')
        .filter(pattern => pattern.trim())
        .map(pattern => pattern.trim());

    const settings = {
        risk_threshold_high: parseFloat(document.getElementById('riskThresholdHigh').value),
        risk_threshold_medium: parseFloat(document.getElementById('riskThresholdMedium').value),
        auto_block_enabled: document.getElementById('autoBlockEnabled').checked,
        ip_whitelist_enabled: document.getElementById('ipWhitelistEnabled').checked,
        suspicious_patterns: patterns
    };

    saveSettings('security', settings);
}

// 保存告警设置
function saveAlertsSettings() {
    const settings = {
        email_enabled: document.getElementById('emailAlertsEnabled').checked,
        webhook_enabled: document.getElementById('webhookAlertsEnabled').checked,
        alert_threshold: parseInt(document.getElementById('alertThreshold').value),
        notification_interval: parseInt(document.getElementById('notificationInterval').value),
        email_address: document.getElementById('alertEmail').value,
        webhook_url: document.getElementById('webhookUrl').value
    };

    saveSettings('alerts', settings);
}

// 通用保存设置函数
function saveSettings(category, settings) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/settings/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            category: category,
            settings: settings
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('设置保存成功', 'success');
        } else {
            showAlert('设置保存失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('保存设置失败:', error);
        showAlert('设置保存失败: ' + error.message, 'danger');
    });
}

// 测试告警
function testAlerts() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/alerts/test', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('测试告警发送成功', 'success');
        } else {
            showAlert('测试告警发送失败: ' + data.message, 'warning');
        }
    })
    .catch(error => {
        console.error('测试告警失败:', error);
        showAlert('测试告警失败: ' + error.message, 'danger');
    });
}

// 系统管理功能
function restartService() {
    if (confirm('确定要重启监控服务吗？这将暂时中断服务。')) {
        showAlert('重启请求已发送，服务将在几秒钟内重启', 'info');
    }
}

function showCleanupModal() {
    if (confirm('确定要清理历史数据吗？此操作不可撤销。')) {
        const token = localStorage.getItem('token');
        if (!token) return;

        fetch('/api/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                confirm_deletion: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('数据清理完成', 'success');
            } else {
                showAlert('数据清理失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('数据清理失败:', error);
            showAlert('数据清理失败: ' + error.message, 'danger');
        });
    }
}

function exportData() {
    const token = localStorage.getItem('token');
    if (!token) return;

    window.open('/api/export/data?token=' + token, '_blank');
    showAlert('数据导出已开始，请检查下载文件夹', 'info');
}

function showResetModal() {
    if (confirm('警告：此操作将重置所有设置和数据，确定继续吗？')) {
        if (confirm('最后确认：此操作不可撤销，确定要重置系统吗？')) {
            showAlert('系统重置功能暂未实现', 'warning');
        }
    }
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
    if (websocket) {
        websocket.close();
    }
});
