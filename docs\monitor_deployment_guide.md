# 监控后台部署指南

## 修复内容概述

本次修复解决了监控后台页面显示全零数据的问题，确保监控系统能够实时显示主系统的运行数据和日志信息。

### 主要修复项目

1. **主系统监控配置修复**
   - 修正了监控后台URL和Token的默认配置
   - 增强了监控日志发送的错误处理和重试机制
   - 添加了详细的中文日志记录

2. **监控后台API优化**
   - 修复了客户统计API的数据处理逻辑
   - 增强了数据库初始化的异常处理
   - 添加了详细的操作日志记录

3. **数据库结构完善**
   - 确保所有必要的数据库表正确创建
   - 添加了数据库初始化的日志记录
   - 优化了客户统计数据的更新机制

4. **前端数据显示优化**
   - 确保API返回的数据格式与前端兼容
   - 优化了统计数据的计算逻辑
   - 添加了错误处理机制

## 部署步骤

### 第一步：上传修复文件

将以下修复后的文件上传到服务器：

```bash
# 主系统监控模块
xbkk/starbucks/src/utils/monitor.py

# 监控后台应用
xbkk/monitor_backend/src/monitor_app.py

# 测试脚本
xbkk/test_monitor_complete.py
```

### 第二步：重启监控后台服务

```bash
# 停止监控后台服务
sudo systemctl stop monitor_backend

# 重新启动监控后台服务
sudo systemctl start monitor_backend

# 检查服务状态
sudo systemctl status monitor_backend

# 查看服务日志
sudo journalctl -u monitor_backend -f
```

### 第三步：重启主系统服务

```bash
# 进入主系统目录
cd /path/to/starbucks

# 停止主系统
pkill -f "python.*main.py"

# 重新启动主系统
nohup python -m src.api.main &

# 检查主系统是否正常运行
curl http://localhost:8000/health
```

### 第四步：验证修复效果

#### 4.1 运行测试脚本

```bash
# 运行完整测试
python test_monitor_complete.py
```

#### 4.2 检查监控页面

1. 访问监控后台页面：`http://************:9094`
2. 使用管理员账号登录：
   - 用户名：admin
   - 密码：admin123
3. 检查以下数据是否正常显示：
   - 总请求数
   - 活跃客户数
   - 可疑请求数
   - 平均响应时间

#### 4.3 测试实时数据更新

1. 调用主系统API生成一些请求：
```bash
# 测试指纹生成API
curl -X GET "http://localhost:8000/api/v1/fingerprint/single" \
     -H "X-API-Key: your_api_key"
```

2. 刷新监控页面，观察数据是否实时更新

## 关键配置说明

### 监控后台配置

- **服务地址**：http://************:9094
- **API Token**：monitor_backend_secret_token_2025
- **数据库文件**：monitor_backend/monitor_logs.db
- **日志文件**：monitor_backend/logs/monitor.log

### 主系统监控配置

主系统会自动向监控后台发送以下数据：
- API请求日志
- 客户访问统计
- 系统性能指标
- 安全告警信息

## 故障排查

### 问题1：监控页面仍显示零数据

**可能原因**：
- 主系统未正确连接到监控后台
- 监控后台API异常
- 数据库连接问题

**解决方法**：
1. 检查主系统日志中是否有监控相关错误
2. 运行测试脚本验证各个组件
3. 检查网络连接和端口开放情况

### 问题2：监控后台服务无法启动

**可能原因**：
- 端口9094被占用
- 数据库文件权限问题
- Python依赖缺失

**解决方法**：
```bash
# 检查端口占用
netstat -tlnp | grep 9094

# 检查数据库文件权限
ls -la monitor_backend/monitor_logs.db

# 安装依赖
pip install -r requirements.txt
```

### 问题3：主系统无法发送监控数据

**可能原因**：
- 监控后台URL配置错误
- Token认证失败
- 网络连接问题

**解决方法**：
1. 检查主系统配置中的监控URL和Token
2. 测试网络连接：`curl http://************:9094/health`
3. 查看主系统日志中的监控相关错误

## 验证清单

部署完成后，请逐项检查以下内容：

- [ ] 监控后台服务正常运行（端口9094）
- [ ] 主系统服务正常运行（端口8000）
- [ ] 监控页面可以正常访问和登录
- [ ] 统计卡片显示非零数据
- [ ] 实时日志页面有数据显示
- [ ] 客户统计页面有数据显示
- [ ] 调用主系统API后监控数据会更新
- [ ] 测试脚本全部通过

## 联系支持

如果在部署过程中遇到问题，请提供以下信息：

1. 服务器系统版本
2. Python版本
3. 错误日志内容
4. 测试脚本运行结果
5. 网络连接测试结果

## 后续维护

### 日志清理

监控后台会持续记录日志数据，建议定期清理：

```bash
# 清理30天前的日志（可根据需要调整）
python -c "
import sqlite3
from datetime import datetime, timedelta

conn = sqlite3.connect('monitor_backend/monitor_logs.db')
cursor = conn.cursor()

cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()
cursor.execute('DELETE FROM api_logs WHERE timestamp < ?', (cutoff_date,))

conn.commit()
conn.close()
print('日志清理完成')
"
```

### 性能监控

建议定期检查以下指标：
- 数据库文件大小
- 监控后台内存使用
- API响应时间
- 错误日志数量

### 备份建议

重要数据备份：
- 数据库文件：monitor_backend/monitor_logs.db
- 配置文件：monitor_backend/src/monitor_app.py
- 日志文件：monitor_backend/logs/
